#!/usr/bin/env python3
"""
Тестовый скрипт для проверки работы клавиатур
"""

import asyncio
import logging
from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton

# Настройка логирования
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

async def test_keyboards():
    """Тестируем создание клавиатур"""
    print("🧪 Тестирование клавиатур...")
    
    try:
        # Тестируем клавиатуру админа
        from admin.keyboards.main import get_admin_main_menu_kb
        admin_kb = get_admin_main_menu_kb()
        print(f"✅ Клавиатура админа: {len(admin_kb.inline_keyboard)} кнопок")
        
        # Тестируем клавиатуру студента
        from student.keyboards.main import get_student_main_menu_kb
        student_kb = get_student_main_menu_kb()
        print(f"✅ Клавиатура студента: {len(student_kb.inline_keyboard)} кнопок")
        
        # Тестируем клавиатуру менеджера
        from manager.keyboards.main import get_manager_main_menu_kb
        manager_kb = get_manager_main_menu_kb()
        print(f"✅ Клавиатура менеджера: {len(manager_kb.inline_keyboard)} кнопок")
        
        # Тестируем клавиатуру куратора
        from curator.keyboards.main import get_curator_main_menu_kb
        curator_kb = get_curator_main_menu_kb()
        print(f"✅ Клавиатура куратора: {len(curator_kb.inline_keyboard)} кнопок")
        
        # Тестируем клавиатуру преподавателя
        from teacher.keyboards.main import get_teacher_main_menu_kb
        teacher_kb = get_teacher_main_menu_kb()
        print(f"✅ Клавиатура преподавателя: {len(teacher_kb.inline_keyboard)} кнопок")
        
        print("🎉 Все клавиатуры создаются успешно!")
        
    except Exception as e:
        print(f"❌ Ошибка при тестировании клавиатур: {e}")
        import traceback
        traceback.print_exc()

async def test_handlers():
    """Тестируем импорт обработчиков"""
    print("🧪 Тестирование обработчиков...")
    
    try:
        # Тестируем импорт функций главных меню
        from admin.handlers.main import show_admin_main_menu
        from student.handlers.main import show_student_main_menu
        from manager.handlers.main import show_manager_main_menu
        from curator.handlers.main import show_curator_main_menu
        from teacher.handlers.main import show_teacher_main_menu
        
        print("✅ Все функции главных меню импортируются успешно!")
        
        # Проверяем сигнатуры функций
        import inspect
        
        sig_admin = inspect.signature(show_admin_main_menu)
        print(f"📝 Сигнатура show_admin_main_menu: {sig_admin}")
        
        sig_student = inspect.signature(show_student_main_menu)
        print(f"📝 Сигнатура show_student_main_menu: {sig_student}")
        
        sig_manager = inspect.signature(show_manager_main_menu)
        print(f"📝 Сигнатура show_manager_main_menu: {sig_manager}")
        
        sig_curator = inspect.signature(show_curator_main_menu)
        print(f"📝 Сигнатура show_curator_main_menu: {sig_curator}")
        
        sig_teacher = inspect.signature(show_teacher_main_menu)
        print(f"📝 Сигнатура show_teacher_main_menu: {sig_teacher}")
        
    except Exception as e:
        print(f"❌ Ошибка при тестировании обработчиков: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """Главная функция теста"""
    print("🚀 Запуск диагностики...")
    
    await test_keyboards()
    print()
    await test_handlers()
    
    print("\n🏁 Диагностика завершена!")

if __name__ == "__main__":
    asyncio.run(main())
