from aiogram import Router, F
from aiogram.types import Message, CallbackQuery
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import StatesGroup, State
from ..keyboards.main import get_manager_main_menu_kb

router = Router()

class ManagerMainStates(StatesGroup):
    main = State()

# Обработчик /start убран - используется общий в main.py


async def show_manager_main_menu(message: Message | CallbackQuery, state: FSMContext = None, user_role: str = None):
    """Показать главное меню менеджера"""
    import logging
    logging.info(f"🔧 show_manager_main_menu вызвана: user_role={user_role}, message_type={type(message).__name__}")

    # Проверяем права доступа (админы тоже могут заходить в меню менеджера)
    if user_role not in ["admin", "manager"]:
        logging.info(f"❌ Доступ запрещен для роли: {user_role}")
        return

    logging.info(f"✅ Отправляем клавиатуру менеджера...")
    text = "👨‍💼 <b>Меню менеджера</b>\n\nВыберите нужный раздел:"

    if isinstance(message, Message):
        await message.answer(text, reply_markup=get_manager_main_menu_kb())
        logging.info(f"📤 Клавиатура менеджера отправлена через message.answer")
    else:  # CallbackQuery
        await message.message.edit_text(text, reply_markup=get_manager_main_menu_kb())
        logging.info(f"📤 Клавиатура менеджера отправлена через message.edit_text")

    if state:
        await state.set_state(ManagerMainStates.main)
        logging.info(f"🔄 Состояние установлено: ManagerMainStates.main")