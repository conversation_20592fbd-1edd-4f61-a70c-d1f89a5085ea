from aiogram import Router, F
from aiogram.types import Message, CallbackQuery
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import StatesGroup, State
from ..keyboards.main import get_curator_main_menu_kb

router = Router()

class CuratorMainStates(StatesGroup):
    main = State()

# Обработчик /start убран - используется общий в main.py

async def show_curator_main_menu(message: Message | CallbackQuery, state: FSMContext = None, user_role: str = None):
    """Показать главное меню куратора"""
    # Проверяем права доступа (админы тоже могут заходить в меню куратора)
    if user_role not in ["admin", "curator"]:
        return

    text = "👨‍🎓 Добро пожаловать в панель <b>куратора</b>!\n\nВыберите действие из меню ниже:"

    if isinstance(message, Message):
        await message.answer(text, reply_markup=get_curator_main_menu_kb())
    else:  # CallbackQuery
        await message.message.edit_text(text, reply_markup=get_curator_main_menu_kb())

    if state:
        await state.set_state(CuratorMainStates.main)